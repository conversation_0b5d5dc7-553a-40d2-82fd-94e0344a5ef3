# No-Code Data Application Builder

An Odoo-inspired no-code data application builder built with Laravel 11+ and Filament 3+, featuring multi-tenancy, AI integration, drag-and-drop builders, and a modular app ecosystem.

## Features

-   **Multi-Tenant Architecture**: Separate PostgreSQL databases per tenant for maximum isolation
-   **AI Integration**: Flexible support for both local (Ollama) and API-based LLMs (OpenRouter, OpenAI, etc.)
-   **Visual Builders**: Drag-and-drop interfaces for forms, tables, and workflows
-   **Modular App Ecosystem**: Odoo-inspired apps (CRM, Sales, Inventory, HR, etc.)
-   **Advanced Database Management**: Schema visualization, query builder, data explorer
-   **Comprehensive Security**: Role-based permissions, audit logging, tenant isolation

## Tech Stack

-   **Backend**: Laravel 12.19.3
-   **Admin Panel**: Laravel Filament 3+
-   **Database**: PostgreSQL (central + per-tenant)
-   **Caching/Queues**: Redis
-   **Frontend**: Livewire, Alpine.js, Tailwind CSS

## Installation

1. **Prerequisites**

    - PHP 8.2+
    - Composer
    - PostgreSQL
    - Redis
    - Node.js & NPM

2. **Environment Setup**

    ```bash
    # Copy and configure environment
    cp .env.example .env

    # Update database configuration in .env
    DB_CONNECTION=pgsql
    DB_HOST=127.0.0.1
    DB_PORT=5432
    DB_DATABASE=nc_central
    DB_USERNAME=postgres
    DB_PASSWORD=your_password
    ```

3. **Install Dependencies**

    ```bash
    composer install
    npm install
    ```

4. **Generate Application Key**
    ```bash
    php artisan key:generate
    ```

## Development Status

This project is currently in **Phase 1: Foundation & Core Multi-Tenancy** development.

### Implementation Phases

-   **Phase 1** (Weeks 1-4): Foundation & Core Multi-Tenancy ⏳
-   **Phase 2** (Weeks 5-8): AI Agent Core Integration
-   **Phase 3** (Weeks 9-14): Data Model & Advanced CRUD Builder
-   **Phase 4** (Weeks 15-18): Advanced Database & Role Management
-   **Phase 5** (Weeks 19-24): Modular App Structure & Workflows

## Project Structure

```
├── app/
│   ├── Http/
│   ├── Models/
│   └── Providers/
├── config/
├── database/
├── resources/
├── routes/
└── prd.md              # Product Requirements Document
```

## Contributing

This project follows the specifications outlined in `prd.md`. Please refer to the PRD for detailed feature requirements and implementation guidelines.

## License

This project is open-sourced software licensed under the [MIT license](https://opensource.org/licenses/MIT).
