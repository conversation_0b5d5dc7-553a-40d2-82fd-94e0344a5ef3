<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Tenant>
 */
class TenantFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $companyName = $this->faker->company();
        $slug = \Illuminate\Support\Str::slug($companyName);

        return [
            'name' => $companyName,
            'slug' => $slug,
            'database_name' => 'nc_tenant_' . $slug,
            'db_username' => 'tenant_' . $slug,
            'db_password' => 'password',
            'is_active' => true,
            'settings' => [
                'timezone' => $this->faker->timezone(),
                'locale' => 'en',
                'theme' => 'default',
            ],
        ];
    }
}
