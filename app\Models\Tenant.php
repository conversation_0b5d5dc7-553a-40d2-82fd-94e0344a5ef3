<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Tenant extends Model
{
    use HasFactory;

    /**
     * The connection name for the model.
     */
    protected $connection = 'central';

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'name',
        'slug',
        'database_name',
        'db_username',
        'db_password',
        'is_active',
        'settings',
    ];

    /**
     * The attributes that should be hidden for serialization.
     */
    protected $hidden = [
        'db_password',
    ];

    /**
     * The attributes that should be cast.
     */
    protected $casts = [
        'is_active' => 'boolean',
        'settings' => 'array',
        'db_password' => 'encrypted',
    ];

    /**
     * Get the tenant's database connection name.
     */
    public function getDatabaseConnectionName(): string
    {
        return "tenant_{$this->id}";
    }

    /**
     * Get the tenant's full database name.
     */
    public function getFullDatabaseName(): string
    {
        return $this->database_name ?: "nc_tenant_{$this->slug}";
    }

    /**
     * Scope to get only active tenants.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }
}
